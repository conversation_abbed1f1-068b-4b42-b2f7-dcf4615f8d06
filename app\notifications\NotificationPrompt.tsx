'use client';

import React, { useState, useEffect } from 'react';
import { useNotifications } from './useNotifications';
import { NotificationConfig } from './types';

interface NotificationPromptProps {
  config: NotificationConfig;
  className?: string;
  showDebugInfo?: boolean;
}

export function NotificationPrompt({ 
  config, 
  className = '',
  showDebugInfo = false 
}: NotificationPromptProps) {
  const {
    state,
    requestPermission,
    subscribe,
    unsubscribe,
    showLocalNotification,
    isLoading,
    error,
  } = useNotifications(config);

  const [isProcessing, setIsProcessing] = useState(false);
  const [showPrompt, setShowPrompt] = useState(false);

  // Update showPrompt when state changes
  useEffect(() => {
    console.log('NotificationPrompt state update:', {
      isLoading,
      isSupported: state.isSupported,
      permission: state.permission,
      showPermissionPrompt: config.showPermissionPrompt,
      shouldShow: !isLoading && state.isSupported && state.permission === 'default' && config.showPermissionPrompt !== false
    });

    if (!isLoading && state.isSupported && state.permission === 'default' && config.showPermissionPrompt !== false) {
      setShowPrompt(true);
    } else {
      setShowPrompt(false);
    }
  }, [state.isSupported, state.permission, isLoading, config.showPermissionPrompt]);

  const handleEnableNotifications = async () => {
    try {
      setIsProcessing(true);
      const permission = await requestPermission();
      
      if (permission === 'granted') {
        setShowPrompt(false);
      }
    } catch (err) {
      console.error('Failed to enable notifications:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDismiss = () => {
    setShowPrompt(false);
  };

  const handleToggleSubscription = async () => {
    try {
      setIsProcessing(true);
      if (state.isSubscribed) {
        await unsubscribe();
      } else {
        if (state.permission !== 'granted') {
          await requestPermission();
        }
        await subscribe();
      }
    } catch (err) {
      console.error('Failed to toggle subscription:', err);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleTestNotification = async () => {
    try {
      await showLocalNotification('Test Notification', {
        body: 'This is a test notification from GamyDay!',
        icon: '/favicon.png',
        tag: 'test',
      });
    } catch (err) {
      console.error('Failed to show test notification:', err);
    }
  };

  // Don't render if notifications are not supported
  if (!state.isSupported) {
    // In development, show debug info even if not supported
    if (process.env.NODE_ENV === 'development') {
      return (
        <div className={`bg-red-600 text-white p-4 rounded-lg shadow-lg ${className}`}>
          <div className="text-sm">
            <strong>Debug:</strong> Push notifications not supported in this browser
            <br />
            <small>User Agent: {navigator.userAgent}</small>
          </div>
        </div>
      );
    }
    return null;
  }

  // Show permission prompt
  if (showPrompt && state.permission === 'default') {
    return (
      <div className={`bg-blue-600 text-white p-4 rounded-lg shadow-lg ${className}`}>
        <div className="flex items-center justify-between">
          <div className="flex-1">
            <h3 className="font-semibold text-sm">Stay Updated!</h3>
            <p className="text-xs mt-1 opacity-90">
              Get notified about new games, tournaments, and updates.
            </p>
          </div>
          <div className="flex gap-2 ml-4">
            <button
              onClick={handleEnableNotifications}
              disabled={isProcessing || isLoading}
              className="bg-white text-blue-600 px-3 py-1 rounded text-xs font-medium hover:bg-gray-100 disabled:opacity-50"
            >
              {isProcessing ? 'Enabling...' : 'Enable'}
            </button>
            <button
              onClick={handleDismiss}
              className="text-white/80 hover:text-white text-xs px-2"
            >
              ✕
            </button>
          </div>
        </div>
        {error && (
          <p className="text-red-200 text-xs mt-2">{error}</p>
        )}
      </div>
    );
  }

  // Debug info (only show if explicitly enabled)
  if (showDebugInfo) {
    return (
      <div className={`bg-gray-800 text-white p-4 rounded-lg ${className}`}>
        <h3 className="font-semibold mb-2">Notification Status</h3>
        <div className="space-y-2 text-sm">
          <div>Permission: <span className="font-mono">{state.permission}</span></div>
          <div>Supported: <span className="font-mono">{state.isSupported ? 'Yes' : 'No'}</span></div>
          <div>Subscribed: <span className="font-mono">{state.isSubscribed ? 'Yes' : 'No'}</span></div>
          {state.subscription && (
            <div>
              <div>Endpoint: <span className="font-mono text-xs break-all">{state.subscription.endpoint}</span></div>
            </div>
          )}
          {error && (
            <div className="text-red-400">Error: {error}</div>
          )}
        </div>
        
        <div className="flex gap-2 mt-4">
          {state.permission === 'granted' && (
            <button
              onClick={handleToggleSubscription}
              disabled={isProcessing}
              className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs disabled:opacity-50"
            >
              {isProcessing 
                ? 'Processing...' 
                : state.isSubscribed 
                  ? 'Unsubscribe' 
                  : 'Subscribe'
              }
            </button>
          )}
          
          {state.permission !== 'granted' && (
            <button
              onClick={handleEnableNotifications}
              disabled={isProcessing}
              className="bg-green-600 hover:bg-green-700 px-3 py-1 rounded text-xs disabled:opacity-50"
            >
              {isProcessing ? 'Requesting...' : 'Request Permission'}
            </button>
          )}
          
          {state.isSubscribed && (
            <button
              onClick={handleTestNotification}
              className="bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-xs"
            >
              Test Notification
            </button>
          )}
        </div>
      </div>
    );
  }

  // In development, always show a debug panel if nothing else is showing
  if (process.env.NODE_ENV === 'development') {
    return (
      <div className={`bg-gray-800 text-white p-4 rounded-lg ${className}`}>
        <h3 className="font-semibold mb-2">🔧 Notification Debug (Dev Only)</h3>
        <div className="space-y-1 text-sm">
          <div>Permission: <span className="font-mono text-yellow-300">{state.permission}</span></div>
          <div>Supported: <span className="font-mono text-yellow-300">{state.isSupported ? 'Yes' : 'No'}</span></div>
          <div>Subscribed: <span className="font-mono text-yellow-300">{state.isSubscribed ? 'Yes' : 'No'}</span></div>
          <div>Loading: <span className="font-mono text-yellow-300">{isLoading ? 'Yes' : 'No'}</span></div>
          <div>Show Prompt: <span className="font-mono text-yellow-300">{showPrompt ? 'Yes' : 'No'}</span></div>
          {error && <div className="text-red-400">Error: {error}</div>}
        </div>

        <div className="flex gap-2 mt-4">
          {state.permission !== 'granted' && (
            <button
              onClick={handleEnableNotifications}
              disabled={isProcessing}
              className="bg-blue-600 hover:bg-blue-700 px-3 py-1 rounded text-xs disabled:opacity-50"
            >
              {isProcessing ? 'Requesting...' : 'Request Permission'}
            </button>
          )}

          {state.isSubscribed && (
            <button
              onClick={handleTestNotification}
              className="bg-purple-600 hover:bg-purple-700 px-3 py-1 rounded text-xs"
            >
              Test Notification
            </button>
          )}
        </div>
      </div>
    );
  }

  // Don't render anything if prompt is dismissed and debug is off
  return null;
}
