// Main notification manager class

import {
  NotificationConfig,
  NotificationPermissionState,
  PushSubscription,
} from './types';
import {
  isPushNotificationSupported,
  isNotificationSupported,
  getNotificationPermission,
  requestNotificationPermission,
  urlBase64ToUint8Array,
  serializePushSubscription,
  sendSubscriptionToServer,
  removeSubscriptionFromServer,
  storeSubscriptionLocally,
  clearStoredSubscription,
} from './utils';

export class NotificationManager {
  private config: NotificationConfig;
  private serviceWorkerRegistration: ServiceWorkerRegistration | null = null;
  private currentSubscription: globalThis.PushSubscription | null = null;

  constructor(config: NotificationConfig) {
    this.config = config;
  }

  /**
   * Initialize the notification manager
   */
  async initialize(): Promise<void> {
    if (!isPushNotificationSupported()) {
      console.warn('Push notifications are not supported in this browser');
      return;
    }

    try {
      // Register service worker
      await this.registerServiceWorker();

      // Check for existing subscription
      await this.checkExistingSubscription();

      // Request permission if configured to do so
      if (this.config.requestPermissionOnLoad) {
        await this.requestPermission();
      }
    } catch (error) {
      console.error('Failed to initialize notification manager:', error);
    }
  }

  /**
   * Register the service worker
   */
  private async registerServiceWorker(): Promise<void> {
    try {
      this.serviceWorkerRegistration = await navigator.serviceWorker.register(
        '/notifications/sw.js',
        { scope: '/notifications/' }
      );

      console.log('Service worker registered successfully');

      // Wait for service worker to be ready
      await navigator.serviceWorker.ready;
    } catch (error) {
      console.error('Service worker registration failed:', error);
      throw error;
    }
  }

  /**
   * Check for existing push subscription
   */
  private async checkExistingSubscription(): Promise<void> {
    if (!this.serviceWorkerRegistration) {
      return;
    }

    try {
      this.currentSubscription = await this.serviceWorkerRegistration.pushManager.getSubscription();
    } catch (error) {
      console.error('Failed to check existing subscription:', error);
    }
  }

  /**
   * Request notification permission from user
   */
  async requestPermission(): Promise<NotificationPermission> {
    const permission = await requestNotificationPermission();
    
    if (permission === 'granted') {
      await this.subscribe();
    }

    return permission;
  }

  /**
   * Subscribe to push notifications
   */
  async subscribe(): Promise<boolean> {
    if (!this.serviceWorkerRegistration) {
      console.error('Service worker not registered');
      return false;
    }

    if (getNotificationPermission() !== 'granted') {
      console.error('Notification permission not granted');
      return false;
    }

    try {
      const applicationServerKey = urlBase64ToUint8Array(this.config.vapidPublicKey);
      
      this.currentSubscription = await this.serviceWorkerRegistration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey,
      });

      const serializedSubscription = serializePushSubscription(this.currentSubscription);
      
      // Store locally for reference
      storeSubscriptionLocally(serializedSubscription);

      // Send to server
      const success = await sendSubscriptionToServer(
        serializedSubscription,
        this.config.serverEndpoint
      );

      if (success) {
        console.log('Successfully subscribed to push notifications');
        return true;
      } else {
        console.error('Failed to send subscription to server');
        return false;
      }
    } catch (error) {
      console.error('Failed to subscribe to push notifications:', error);
      return false;
    }
  }

  /**
   * Unsubscribe from push notifications
   */
  async unsubscribe(): Promise<boolean> {
    if (!this.currentSubscription) {
      console.log('No active subscription to unsubscribe from');
      return true;
    }

    try {
      const serializedSubscription = serializePushSubscription(this.currentSubscription);
      
      // Remove from server first
      await removeSubscriptionFromServer(
        serializedSubscription,
        this.config.serverEndpoint
      );

      // Unsubscribe locally
      const success = await this.currentSubscription.unsubscribe();
      
      if (success) {
        this.currentSubscription = null;
        clearStoredSubscription();
        console.log('Successfully unsubscribed from push notifications');
      }

      return success;
    } catch (error) {
      console.error('Failed to unsubscribe from push notifications:', error);
      return false;
    }
  }

  /**
   * Get current notification state
   */
  getState(): NotificationPermissionState {
    const permission = getNotificationPermission();
    const isSupported = isPushNotificationSupported() && isNotificationSupported();
    const isSubscribed = !!this.currentSubscription;
    const subscription = this.currentSubscription 
      ? serializePushSubscription(this.currentSubscription)
      : null;

    return {
      permission,
      isSupported,
      isSubscribed,
      subscription,
    };
  }

  /**
   * Show a local notification (for testing)
   */
  async showLocalNotification(title: string, options?: NotificationOptions): Promise<void> {
    if (getNotificationPermission() !== 'granted') {
      console.error('Notification permission not granted');
      return;
    }

    if (!this.serviceWorkerRegistration) {
      console.error('Service worker not registered');
      return;
    }

    try {
      await this.serviceWorkerRegistration.showNotification(title, {
        icon: '/favicon.png',
        badge: '/favicon.png',
        ...options,
      });
    } catch (error) {
      console.error('Failed to show notification:', error);
    }
  }

  /**
   * Update configuration
   */
  updateConfig(newConfig: Partial<NotificationConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
