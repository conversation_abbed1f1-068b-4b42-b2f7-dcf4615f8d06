# GamyDay Web Push Notification System - Complete Setup Guide

This guide will walk you through setting up the complete zero-cost web push notification system for GamyDay, including both frontend and backend components.

## 🎯 Overview

The notification system consists of:
- **Frontend Module**: Self-contained notification module in your Next.js app
- **Backend Server**: Python FastAPI server with admin panel
- **Database**: MongoDB for storing subscriptions and notification history

## 📋 Prerequisites

- Node.js 18+ and npm/yarn
- Python 3.11+
- MongoDB Atlas account (free tier)
- Git

## 🚀 Quick Start (5 Minutes)

### Step 1: Backend Setup

1. **Navigate to the backend directory:**
   ```bash
   cd gamyday-notification-server
   ```

2. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Generate VAPID keys:**
   ```bash
   python scripts/generate_vapid_keys.py
   ```
   Copy the generated keys for later use.

4. **Create environment file:**
   ```bash
   cp .env.example .env
   ```

5. **Edit `.env` with your configuration:**
   ```env
   MONGODB_URL=mongodb+srv://username:<EMAIL>/gamyday_notifications
   VAPID_PUBLIC_KEY=your-generated-public-key
   VAPID_PRIVATE_KEY=your-generated-private-key
   VAPID_SUBJECT=mailto:<EMAIL>
   ALLOWED_ORIGINS=http://localhost:3000
   ADMIN_USERNAME=admin
   ADMIN_PASSWORD=your-secure-password
   ```

6. **Setup database:**
   ```bash
   python scripts/setup_database.py
   ```

7. **Start the server:**
   ```bash
   python main.py
   ```

### Step 2: Frontend Setup

1. **Navigate to the frontend directory:**
   ```bash
   cd gamyday-frontend
   ```

2. **Update environment variables:**
   Add to your `.env.local`:
   ```env
   NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-generated-public-key
   NEXT_PUBLIC_NOTIFICATION_SERVER_URL=http://localhost:8000
   ```

3. **Start the frontend:**
   ```bash
   npm run dev
   ```

### Step 3: Test the System

1. Open `http://localhost:3000` in your browser
2. You should see a notification permission prompt
3. Click "Enable" to grant permission
4. Open the admin panel at `http://localhost:8000/admin/`
5. Login with your admin credentials
6. Send a test notification

## 🗄️ MongoDB Setup (Free Tier)

### Option 1: MongoDB Atlas (Recommended)

1. **Create account:** Go to [MongoDB Atlas](https://www.mongodb.com/atlas)
2. **Create cluster:** Choose the free M0 tier
3. **Create database user:**
   - Go to Database Access
   - Add new user with read/write permissions
4. **Configure network access:**
   - Go to Network Access
   - Add IP address (0.0.0.0/0 for development)
5. **Get connection string:**
   - Go to Clusters → Connect → Connect your application
   - Copy the connection string
   - Replace `<password>` with your user password

### Option 2: Local MongoDB

```bash
# Install MongoDB
# Ubuntu/Debian
sudo apt-get install mongodb

# macOS
brew install mongodb-community

# Start MongoDB
sudo systemctl start mongodb  # Linux
brew services start mongodb-community  # macOS

# Use local connection string
MONGODB_URL=mongodb://localhost:27017/gamyday_notifications
```

## 🚀 Deployment

### Backend Deployment

#### Option 1: Vercel (Recommended)

1. **Install Vercel CLI:**
   ```bash
   npm i -g vercel
   ```

2. **Deploy:**
   ```bash
   cd gamyday-notification-server
   vercel --prod
   ```

3. **Set environment variables in Vercel dashboard:**
   - Go to your project settings
   - Add all environment variables from your `.env` file

#### Option 2: Render

1. **Connect GitHub repository to Render**
2. **Use the provided `render.yaml` configuration**
3. **Set environment variables in Render dashboard**

#### Option 3: Railway

1. **Install Railway CLI:**
   ```bash
   npm i -g @railway/cli
   ```

2. **Deploy:**
   ```bash
   cd gamyday-notification-server
   railway login
   railway up
   ```

### Frontend Deployment

#### Vercel (Recommended)

1. **Deploy frontend:**
   ```bash
   cd gamyday-frontend
   vercel --prod
   ```

2. **Update environment variables:**
   - Set `NEXT_PUBLIC_NOTIFICATION_SERVER_URL` to your deployed backend URL
   - Set `NEXT_PUBLIC_VAPID_PUBLIC_KEY` to your VAPID public key

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```env
# Database
MONGODB_URL=mongodb+srv://...

# VAPID Keys
VAPID_PUBLIC_KEY=BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuOmLWjMpS_7QX0-SJl6FxOaujKy6yOLXmYWNWBdXYFOuY
VAPID_PRIVATE_KEY=your-private-key
VAPID_SUBJECT=mailto:<EMAIL>

# Server
PORT=8000
HOST=0.0.0.0
DEBUG=False

# CORS
ALLOWED_ORIGINS=https://your-frontend-domain.com

# Admin
ADMIN_USERNAME=admin
ADMIN_PASSWORD=secure-password-123

# Security
SECRET_KEY=your-secret-key
```

#### Frontend (.env.local)
```env
NEXT_PUBLIC_VAPID_PUBLIC_KEY=BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuOmLWjMpS_7QX0-SJl6FxOaujKy6yOLXmYWNWBdXYFOuY
NEXT_PUBLIC_NOTIFICATION_SERVER_URL=https://your-backend-domain.com
```

## 🎛️ Admin Panel Usage

### Accessing the Admin Panel

1. Go to `https://your-backend-domain.com/admin/`
2. Login with your admin credentials
3. You'll see the dashboard with statistics

### Sending Notifications

1. **Go to "Send Notification"**
2. **Fill in the form:**
   - Title: Short, catchy title
   - Message: Notification body text
   - Click URL: Where users go when they click
   - Icon: URL to notification icon
   - Target: Who receives the notification

3. **Preview and send**

### Managing Subscriptions

- **View all subscriptions** in the Subscriptions tab
- **Export data** as CSV or JSON
- **Monitor activity** and notification counts

## 🔍 Testing

### Local Testing

1. **Start both servers:**
   ```bash
   # Terminal 1 - Backend
   cd gamyday-notification-server
   python main.py

   # Terminal 2 - Frontend
   cd gamyday-frontend
   npm run dev
   ```

2. **Test the flow:**
   - Visit `http://localhost:3000`
   - Grant notification permission
   - Send test notification from admin panel
   - Verify notification appears

### Production Testing

1. **Deploy both components**
2. **Update CORS settings** in backend
3. **Test with real domain**
4. **Verify HTTPS works** (required for notifications)

## 🛠️ Troubleshooting

### Common Issues

#### 1. Notifications Not Working
- **Check browser support** (Chrome, Firefox, Safari 16.4+)
- **Verify HTTPS** in production
- **Check VAPID keys** are correct
- **Verify service worker** is registered

#### 2. CORS Errors
- **Add frontend domain** to `ALLOWED_ORIGINS`
- **Include protocol** (http/https)
- **Check for typos** in domain names

#### 3. Database Connection Issues
- **Verify MongoDB URL** format
- **Check network access** in MongoDB Atlas
- **Test connection** using MongoDB Compass

#### 4. Admin Panel Not Loading
- **Check admin credentials**
- **Verify templates** directory exists
- **Check server logs** for errors

### Debug Mode

Enable debug information in the frontend:

```tsx
<NotificationPrompt 
  config={getNotificationConfig()}
  showDebugInfo={true}
/>
```

## 📊 Monitoring

### Server Logs

```bash
# Local development
python main.py

# Docker
docker-compose logs notification-server

# Vercel
vercel logs

# Render
Check Render dashboard logs
```

### Analytics

The admin panel provides:
- Total subscriptions
- Active subscriptions
- Notifications sent today
- Recent notification history
- Success/failure rates

## 🔒 Security Best Practices

1. **Change default admin password**
2. **Use strong VAPID keys**
3. **Set proper CORS origins**
4. **Use HTTPS in production**
5. **Keep dependencies updated**
6. **Monitor logs regularly**
7. **Backup your database**

## 🚀 Going Live Checklist

- [ ] Backend deployed and accessible
- [ ] Frontend deployed with correct environment variables
- [ ] MongoDB Atlas configured with proper access
- [ ] VAPID keys generated and configured
- [ ] Admin credentials changed from defaults
- [ ] CORS origins set to production domains
- [ ] HTTPS enabled on both frontend and backend
- [ ] Test notification flow works end-to-end
- [ ] Admin panel accessible and functional

## 📞 Support

If you encounter issues:

1. **Check this guide** for common solutions
2. **Review server logs** for error messages
3. **Test with debug mode** enabled
4. **Verify environment variables** are correct
5. **Check browser console** for client-side errors

## 🎉 Success!

Once everything is set up, you'll have:
- ✅ Automatic notification permission requests
- ✅ Beautiful admin panel for sending notifications
- ✅ MongoDB storage for subscriptions and history
- ✅ Zero-cost hosting on free platforms
- ✅ Secure, production-ready notification system

Your users will now receive push notifications about new games, tournaments, and updates!
