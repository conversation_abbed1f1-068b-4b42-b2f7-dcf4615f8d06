// Utility functions for web push notifications

import { PushSubscription } from './types';

/**
 * Convert a VAPID public key from base64 to Uint8Array
 */
export function urlBase64ToUint8Array(base64String: string): Uint8Array {
  const padding = '='.repeat((4 - base64String.length % 4) % 4);
  const base64 = (base64String + padding)
    .replace(/-/g, '+')
    .replace(/_/g, '/');

  const rawData = window.atob(base64);
  const outputArray = new Uint8Array(rawData.length);

  for (let i = 0; i < rawData.length; ++i) {
    outputArray[i] = rawData.charCodeAt(i);
  }
  return outputArray;
}

/**
 * Check if the browser supports push notifications
 */
export function isPushNotificationSupported(): boolean {
  return 'serviceWorker' in navigator && 'PushManager' in window;
}

/**
 * Check if the browser supports notifications
 */
export function isNotificationSupported(): boolean {
  return 'Notification' in window;
}

/**
 * Get the current notification permission status
 */
export function getNotificationPermission(): NotificationPermission {
  if (!isNotificationSupported()) {
    return 'denied';
  }
  return Notification.permission;
}

/**
 * Request notification permission from the user
 */
export async function requestNotificationPermission(): Promise<NotificationPermission> {
  if (!isNotificationSupported()) {
    return 'denied';
  }

  if (Notification.permission === 'default') {
    const permission = await Notification.requestPermission();
    return permission;
  }

  return Notification.permission;
}

/**
 * Convert PushSubscription to a serializable object
 */
export function serializePushSubscription(subscription: globalThis.PushSubscription): PushSubscription {
  const key = subscription.getKey('p256dh');
  const token = subscription.getKey('auth');

  return {
    endpoint: subscription.endpoint,
    keys: {
      p256dh: key ? btoa(String.fromCharCode(...new Uint8Array(key))) : '',
      auth: token ? btoa(String.fromCharCode(...new Uint8Array(token))) : '',
    },
  };
}

/**
 * Send subscription data to the server
 */
export async function sendSubscriptionToServer(
  subscription: PushSubscription,
  serverEndpoint: string
): Promise<boolean> {
  try {
    const response = await fetch(`${serverEndpoint}/api/subscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscription,
        timestamp: new Date().toISOString(),
      }),
    });

    return response.ok;
  } catch (error) {
    console.error('Failed to send subscription to server:', error);
    return false;
  }
}

/**
 * Remove subscription from the server
 */
export async function removeSubscriptionFromServer(
  subscription: PushSubscription,
  serverEndpoint: string
): Promise<boolean> {
  try {
    const response = await fetch(`${serverEndpoint}/api/unsubscribe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        subscription,
      }),
    });

    return response.ok;
  } catch (error) {
    console.error('Failed to remove subscription from server:', error);
    return false;
  }
}

/**
 * Store subscription in localStorage for debugging/reference
 */
export function storeSubscriptionLocally(subscription: PushSubscription): void {
  try {
    localStorage.setItem('push-subscription', JSON.stringify(subscription));
  } catch (error) {
    console.warn('Failed to store subscription locally:', error);
  }
}

/**
 * Get stored subscription from localStorage
 */
export function getStoredSubscription(): PushSubscription | null {
  try {
    const stored = localStorage.getItem('push-subscription');
    return stored ? JSON.parse(stored) : null;
  } catch (error) {
    console.warn('Failed to get stored subscription:', error);
    return null;
  }
}

/**
 * Clear stored subscription from localStorage
 */
export function clearStoredSubscription(): void {
  try {
    localStorage.removeItem('push-subscription');
  } catch (error) {
    console.warn('Failed to clear stored subscription:', error);
  }
}
