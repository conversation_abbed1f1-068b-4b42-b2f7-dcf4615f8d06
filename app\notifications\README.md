# GamyDay Frontend Notification Module

A self-contained web push notification module for the GamyDay frontend application. This module handles user permission requests, service worker registration, and push subscription management.

## Features

- 🔔 **Automatic permission requests** when the app loads
- 🎨 **Beautiful notification prompts** with Tailwind CSS styling
- 🔧 **Self-contained and removable** - doesn't affect existing code
- 📱 **Service worker integration** for background notifications
- 🔒 **Secure VAPID key handling** with environment variables
- 🎯 **TypeScript support** with full type definitions
- 🪝 **React hooks** for easy integration

## Quick Setup

### 1. Environment Variables

Add these to your `.env.local` file:

```env
NEXT_PUBLIC_VAPID_PUBLIC_KEY=your-vapid-public-key
NEXT_PUBLIC_NOTIFICATION_SERVER_URL=https://your-notification-server.com
```

### 2. Service Worker

The service worker is automatically registered and located at `/public/notifications/sw.js`. No additional setup required.

### 3. Integration

The notification module is already integrated into the main layout (`app/RootLayout.tsx`). The notification prompt will automatically appear for users who haven't granted permission yet.

## Module Structure

```
app/notifications/
├── README.md                 # This documentation
├── index.ts                  # Main exports
├── types.ts                  # TypeScript type definitions
├── utils.ts                  # Utility functions
├── config.ts                 # Configuration settings
├── NotificationManager.ts    # Core notification manager class
├── useNotifications.ts       # React hook for notifications
└── NotificationPrompt.tsx    # React component for UI prompt
```

## Usage

### Basic Usage (Already Integrated)

The notification system is already integrated into your app. Users will see a notification prompt when they visit the site.

### Custom Usage

If you want to use the notification system in other components:

```tsx
import { useNotifications, getNotificationConfig } from './notifications';

function MyComponent() {
  const {
    state,
    requestPermission,
    subscribe,
    unsubscribe,
    showLocalNotification,
    isLoading,
    error,
  } = useNotifications(getNotificationConfig());

  const handleEnableNotifications = async () => {
    try {
      const permission = await requestPermission();
      if (permission === 'granted') {
        console.log('Notifications enabled!');
      }
    } catch (error) {
      console.error('Failed to enable notifications:', error);
    }
  };

  return (
    <div>
      <p>Permission: {state.permission}</p>
      <p>Subscribed: {state.isSubscribed ? 'Yes' : 'No'}</p>
      <button onClick={handleEnableNotifications}>
        Enable Notifications
      </button>
    </div>
  );
}
```

### Manual Notification Manager

For advanced usage, you can use the NotificationManager directly:

```tsx
import { NotificationManager, getNotificationConfig } from './notifications';

const manager = new NotificationManager(getNotificationConfig());

// Initialize
await manager.initialize();

// Request permission and subscribe
await manager.requestPermission();

// Show a local test notification
await manager.showLocalNotification('Test', {
  body: 'This is a test notification',
  icon: '/favicon.png',
});

// Get current state
const state = manager.getState();
console.log('Notification state:', state);
```

## Configuration

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `NEXT_PUBLIC_VAPID_PUBLIC_KEY` | VAPID public key from your server | Yes | - |
| `NEXT_PUBLIC_NOTIFICATION_SERVER_URL` | URL of your notification server | Yes | http://localhost:8000 |

### Configuration Options

You can customize the notification behavior by modifying `app/notifications/config.ts`:

```typescript
export const notificationConfig: NotificationConfig = {
  vapidPublicKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || '',
  serverEndpoint: process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_URL || '',
  requestPermissionOnLoad: true,  // Auto-request permission
  showPermissionPrompt: true,     // Show UI prompt
};
```

## Components

### NotificationPrompt

The main UI component that shows permission prompts to users.

**Props:**
- `config: NotificationConfig` - Notification configuration
- `className?: string` - Additional CSS classes
- `showDebugInfo?: boolean` - Show debug information

**Usage:**
```tsx
<NotificationPrompt 
  config={getNotificationConfig()}
  className="fixed top-4 right-4"
  showDebugInfo={false}
/>
```

## Hooks

### useNotifications

React hook for managing notification state and actions.

**Parameters:**
- `config: NotificationConfig` - Notification configuration

**Returns:**
```typescript
{
  state: NotificationPermissionState;
  requestPermission: () => Promise<NotificationPermission>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  showLocalNotification: (title: string, options?: NotificationOptions) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}
```

## Service Worker

The service worker (`/public/notifications/sw.js`) handles:

- **Push events** - Receiving and displaying notifications
- **Notification clicks** - Opening the app when notifications are clicked
- **Background sync** - Handling offline scenarios
- **Caching** - Caching notification assets

### Service Worker Features

- Automatic notification display
- Click handling with URL navigation
- Error handling and retry logic
- Background sync support
- Cache management

## Customization

### Styling

The notification prompt uses Tailwind CSS classes. You can customize the appearance by:

1. **Modifying the component** - Edit `NotificationPrompt.tsx`
2. **Adding custom classes** - Use the `className` prop
3. **Overriding styles** - Add custom CSS

### Notification Appearance

Customize notification appearance in the service worker:

```javascript
// In /public/notifications/sw.js
const notificationOptions = {
  icon: '/your-custom-icon.png',
  badge: '/your-custom-badge.png',
  vibrate: [200, 100, 200],
  // ... other options
};
```

## Removal

To remove the notification module:

1. **Remove from layout:**
   ```tsx
   // In app/RootLayout.tsx, remove these lines:
   import { NotificationPrompt, getNotificationConfig } from "./notifications";
   
   // And remove the NotificationPrompt component
   ```

2. **Delete the module:**
   ```bash
   rm -rf app/notifications/
   rm public/notifications/sw.js
   ```

3. **Clean up environment variables:**
   ```bash
   # Remove from .env.local
   NEXT_PUBLIC_VAPID_PUBLIC_KEY=
   NEXT_PUBLIC_NOTIFICATION_SERVER_URL=
   ```

## Browser Support

- **Chrome/Edge**: Full support
- **Firefox**: Full support
- **Safari**: Limited support (iOS 16.4+)
- **Mobile browsers**: Varies by platform

## Security

- VAPID keys are used for secure communication
- Service worker runs in a secure context (HTTPS required in production)
- No sensitive data is stored in localStorage
- All API calls use HTTPS in production

## Troubleshooting

### Common Issues

1. **Notifications not working**
   - Check browser support
   - Verify HTTPS in production
   - Check VAPID keys

2. **Permission denied**
   - User must manually enable in browser settings
   - Clear site data and try again

3. **Service worker not registering**
   - Check file path: `/public/notifications/sw.js`
   - Verify HTTPS in production
   - Check browser console for errors

### Debug Mode

Enable debug information:

```tsx
<NotificationPrompt 
  config={getNotificationConfig()}
  showDebugInfo={true}
/>
```

This will show:
- Current permission status
- Subscription state
- Error messages
- Test notification button

## API Reference

See the TypeScript definitions in `types.ts` for complete API documentation.

## Examples

### Testing Notifications

```tsx
// Show a test notification
await showLocalNotification('Test Notification', {
  body: 'This is a test message',
  icon: '/favicon.png',
  tag: 'test',
  requireInteraction: true,
});
```

### Checking Permission Status

```tsx
const { state } = useNotifications(config);

if (state.permission === 'granted' && state.isSubscribed) {
  console.log('User is subscribed to notifications');
} else if (state.permission === 'denied') {
  console.log('User has denied notification permission');
} else {
  console.log('Permission not yet requested');
}
```
