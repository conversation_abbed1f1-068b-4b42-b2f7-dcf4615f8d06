// Configuration for the notification system

import { NotificationConfig } from './types';

// Default VAPID public key (this should be replaced with your actual key)
const DEFAULT_VAPID_PUBLIC_KEY = 'BEl62iUYgUivxIkv69yViEuiBIa40HI80NM9f8HnKJuOmLWjMpS_7QX0-SJl6FxOaujKy6yOLXmYWNWBdXYFOuY';

// Default server endpoint (this should be replaced with your actual server URL)
const DEFAULT_SERVER_ENDPOINT = process.env.NODE_ENV === 'production' 
  ? 'https://your-notification-server.vercel.app'
  : 'http://localhost:8000';

export const notificationConfig: NotificationConfig = {
  vapidPublicKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY || DEFAULT_VAPID_PUBLIC_KEY,
  serverEndpoint: process.env.NEXT_PUBLIC_NOTIFICATION_SERVER_URL || DEFAULT_SERVER_ENDPOINT,
  requestPermissionOnLoad: true,
  showPermissionPrompt: true,
};

// Environment-specific configurations
export const getNotificationConfig = (): NotificationConfig => {
  return {
    ...notificationConfig,
    // Override settings based on environment
    requestPermissionOnLoad: process.env.NODE_ENV === 'production',
    showPermissionPrompt: true,
  };
};
