'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { NotificationManager } from './NotificationManager';
import { NotificationConfig, NotificationPermissionState } from './types';

interface UseNotificationsReturn {
  state: NotificationPermissionState;
  requestPermission: () => Promise<NotificationPermission>;
  subscribe: () => Promise<boolean>;
  unsubscribe: () => Promise<boolean>;
  showLocalNotification: (title: string, options?: NotificationOptions) => Promise<void>;
  isLoading: boolean;
  error: string | null;
}

export function useNotifications(config: NotificationConfig): UseNotificationsReturn {
  const [state, setState] = useState<NotificationPermissionState>({
    permission: 'default',
    isSupported: false,
    isSubscribed: false,
    subscription: null,
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  const managerRef = useRef<NotificationManager | null>(null);

  // Initialize notification manager
  useEffect(() => {
    const initializeManager = async () => {
      try {
        setIsLoading(true);
        setError(null);

        managerRef.current = new NotificationManager(config);
        await managerRef.current.initialize();
        
        // Update state
        setState(managerRef.current.getState());
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to initialize notifications');
        console.error('Notification manager initialization error:', err);
      } finally {
        setIsLoading(false);
      }
    };

    initializeManager();
  }, [config]);

  // Update state when manager changes
  const updateState = useCallback(() => {
    if (managerRef.current) {
      setState(managerRef.current.getState());
    }
  }, []);

  const requestPermission = useCallback(async (): Promise<NotificationPermission> => {
    if (!managerRef.current) {
      throw new Error('Notification manager not initialized');
    }

    try {
      setError(null);
      const permission = await managerRef.current.requestPermission();
      updateState();
      return permission;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to request permission';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [updateState]);

  const subscribe = useCallback(async (): Promise<boolean> => {
    if (!managerRef.current) {
      throw new Error('Notification manager not initialized');
    }

    try {
      setError(null);
      const success = await managerRef.current.subscribe();
      updateState();
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to subscribe';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [updateState]);

  const unsubscribe = useCallback(async (): Promise<boolean> => {
    if (!managerRef.current) {
      throw new Error('Notification manager not initialized');
    }

    try {
      setError(null);
      const success = await managerRef.current.unsubscribe();
      updateState();
      return success;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to unsubscribe';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [updateState]);

  const showLocalNotification = useCallback(async (
    title: string, 
    options?: NotificationOptions
  ): Promise<void> => {
    if (!managerRef.current) {
      throw new Error('Notification manager not initialized');
    }

    try {
      setError(null);
      await managerRef.current.showLocalNotification(title, options);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to show notification';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  return {
    state,
    requestPermission,
    subscribe,
    unsubscribe,
    showLocalNotification,
    isLoading,
    error,
  };
}
