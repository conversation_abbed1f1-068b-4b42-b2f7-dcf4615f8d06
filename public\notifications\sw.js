// Service Worker for Push Notifications
// This file handles push notifications in the background

const CACHE_NAME = 'gamyday-notifications-v1';
const NOTIFICATION_TAG = 'gamyday-notification';

// Install event - cache essential resources
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  
  event.waitUntil(
    caches.open(CACHE_NAME).then((cache) => {
      return cache.addAll([
        '/favicon.png',
        // Add other essential resources here
      ]);
    })
  );
  
  // Take control immediately
  self.skipWaiting();
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== CACHE_NAME) {
            console.log('Deleting old cache:', cacheName);
            return caches.delete(cacheName);
          }
        })
      );
    })
  );
  
  // Take control of all clients
  self.clients.claim();
});

// Push event - handle incoming push notifications
self.addEventListener('push', (event) => {
  console.log('Push event received:', event);
  
  let notificationData = {
    title: 'GamyDay Notification',
    body: 'You have a new notification!',
    icon: '/favicon.png',
    badge: '/favicon.png',
    tag: NOTIFICATION_TAG,
    data: {},
    actions: [],
    requireInteraction: false,
  };

  // Parse push data if available
  if (event.data) {
    try {
      const pushData = event.data.json();
      notificationData = {
        ...notificationData,
        ...pushData,
      };
    } catch (error) {
      console.error('Error parsing push data:', error);
      // Use text data as body if JSON parsing fails
      notificationData.body = event.data.text() || notificationData.body;
    }
  }

  // Show the notification
  event.waitUntil(
    self.registration.showNotification(notificationData.title, {
      body: notificationData.body,
      icon: notificationData.icon,
      badge: notificationData.badge,
      tag: notificationData.tag,
      data: notificationData.data,
      actions: notificationData.actions,
      requireInteraction: notificationData.requireInteraction,
      silent: false,
      vibrate: [200, 100, 200],
    })
  );
});

// Notification click event - handle user interaction
self.addEventListener('notificationclick', (event) => {
  console.log('Notification clicked:', event);
  
  const notification = event.notification;
  const action = event.action;
  const data = notification.data || {};
  
  // Close the notification
  notification.close();
  
  // Handle different actions
  let urlToOpen = '/';
  
  if (action) {
    // Handle specific action buttons
    switch (action) {
      case 'view':
        urlToOpen = data.url || '/';
        break;
      case 'dismiss':
        // Just close, don't open anything
        return;
      default:
        urlToOpen = data.url || '/';
    }
  } else {
    // Handle main notification click
    urlToOpen = data.url || '/';
  }
  
  // Open or focus the app
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then((clientList) => {
      // Check if there's already a window/tab open with the target URL
      for (const client of clientList) {
        if (client.url === urlToOpen && 'focus' in client) {
          return client.focus();
        }
      }
      
      // If no existing window/tab, open a new one
      if (clients.openWindow) {
        return clients.openWindow(urlToOpen);
      }
    })
  );
});

// Notification close event - handle when user dismisses notification
self.addEventListener('notificationclose', (event) => {
  console.log('Notification closed:', event);
  
  const notification = event.notification;
  const data = notification.data || {};
  
  // Track notification dismissal if needed
  if (data.trackDismissal) {
    // Send analytics or tracking data
    fetch('/api/notifications/track', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        action: 'dismissed',
        notificationId: data.id,
        timestamp: new Date().toISOString(),
      }),
    }).catch((error) => {
      console.error('Failed to track notification dismissal:', error);
    });
  }
});

// Background sync event - handle offline actions
self.addEventListener('sync', (event) => {
  console.log('Background sync event:', event);
  
  if (event.tag === 'notification-sync') {
    event.waitUntil(
      // Handle any pending notification-related sync tasks
      syncNotificationData()
    );
  }
});

// Helper function to sync notification data
async function syncNotificationData() {
  try {
    // Implement any background sync logic here
    // For example, sending queued analytics data
    console.log('Syncing notification data...');
  } catch (error) {
    console.error('Failed to sync notification data:', error);
  }
}

// Message event - handle messages from the main thread
self.addEventListener('message', (event) => {
  console.log('Service Worker received message:', event.data);
  
  const { type, payload } = event.data;
  
  switch (type) {
    case 'SKIP_WAITING':
      self.skipWaiting();
      break;
    case 'GET_VERSION':
      event.ports[0].postMessage({ version: CACHE_NAME });
      break;
    case 'CLEAR_NOTIFICATIONS':
      // Clear all notifications with our tag
      self.registration.getNotifications({ tag: NOTIFICATION_TAG }).then((notifications) => {
        notifications.forEach((notification) => notification.close());
      });
      break;
    default:
      console.log('Unknown message type:', type);
  }
});

// Error event - handle service worker errors
self.addEventListener('error', (event) => {
  console.error('Service Worker error:', event);
});

// Unhandled rejection event
self.addEventListener('unhandledrejection', (event) => {
  console.error('Service Worker unhandled rejection:', event);
});
